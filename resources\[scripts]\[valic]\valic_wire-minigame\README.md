# Valic Wiring Minigame

Wiring minigame pro FiveM ve stylu Among Us. Hr<PERSON>či musí propojovat kabely stejných barev pomocí drag & drop rozhraní.

## 🎮 Funkce

- **Among Us styl**: Propojo<PERSON><PERSON> kabelů drag & drop
- **Konfigurovatelné**: <PERSON><PERSON><PERSON> kabel<PERSON>, timeout, barvy
- **Debug mód**: Výpisy pro vývoj a testování
- **Zvuky**: Úspěch/neúspěch ve formátu .ogg
- **Penalizace**: Špatné spojení resetuje celou hru
- **Exporty**: Snadná integrace do jiných scriptů

## 📁 Struktura

```
valic_wire-minigame/
├── fxmanifest.lua      # Manifest soubor
├── config.lua          # Konfigurace
├── client/
│   └── main.lua        # Hlavní logika
├── html/
│   ├── index.html      # UI rozhraní
│   ├── style.css       # Styling
│   └── script.js       # JavaScript logika
├── src/
│   ├── success.ogg     # Zvuk ú<PERSON>ěchu
│   └── fail.ogg        # Zvuk neúspěchu
└── README.md           # Dokumentace
```

## ⚙️ Konfigurace

Upravte `config.lua` podle potřeb:

```lua
Config = {
    TimeoutSeconds = 0,     -- 0 = nekonečno, jinak sekundy
    CableCount = 5,         -- počet kabelů (3-10 doporučeno)
    DevMode = true,         -- debug výpisy
    
    Colors = {              -- 32 barev pro kabely
        "#000000", "#00021c", "#1c284d", ...
    }
}
```

## 🚀 Instalace

1. Zkopírujte složku do `resources/[scripts]/[valic]/`
2. Přidejte do `server.cfg`: `ensure valic_wire-minigame`
3. Nahraďte placeholder zvuky v `src/` skutečnými .ogg soubory
4. Restartujte server

## 🎯 Použití

### Testování
```
/test_wiring
```

### Exporty

```lua
-- Spuštění minigry
exports['valic_wire-minigame']:startWiring(playerId)

-- Zastavení minigry
exports['valic_wire-minigame']:stopWiring(playerId, success)
```

### Eventy

```lua
-- Úspěšné dokončení
RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    -- Vaše logika pro úspěch
end)

-- Neúspěšné dokončení
RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    -- Vaše logika pro neúspěch
end)
```

## 🎮 Jak hrát

1. Spusťte minigru pomocí exportu nebo příkazu `/test_wiring`
2. Táhněte konektory z levé strany na odpovídající barvy vpravo
3. Špatné spojení resetuje celou hru
4. Dokončte všechna spojení pro úspěch

## 🔧 Debug mód

Zapněte `Config.DevMode = true` pro:
- Výpisy do konzole
- Debug informace v UI
- Sledování jednotlivých kroků

## 🎵 Zvuky

Nahraďte placeholder soubory v `src/`:
- `success.ogg` - zvuk úspěchu
- `fail.ogg` - zvuk neúspěchu

Podporované formáty: .ogg (doporučeno pro FiveM)

## 🔗 Integrace

### Příklad použití v jiném scriptu:

```lua
-- Spuštění wiring úkolu
local function startWiringTask(player)
    local success = exports['valic_wire-minigame']:startWiring(GetPlayerServerId(player))
    if not success then
        -- Minihra už běží nebo chyba
    end
end

-- Reakce na dokončení
RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    -- Hráč úspěšně dokončil wiring
    -- Pokračujte s dalším úkolem
end)

RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    -- Hráč selhal
    -- Můžete přidat penalizaci nebo restart
end)
```

## 📝 Poznámky

- Kompatibilní s QB-Core a QBox-Core
- Optimalizováno pro výkon
- Responzivní design
- Plně konfigurovatelné

## 🐛 Řešení problémů

1. **Minihra se nespustí**: Zkontrolujte konzoli pro chyby
2. **Zvuky nehrají**: Ověřte .ogg soubory v `src/`
3. **UI se nezobrazuje**: Zkontrolujte `files` v fxmanifest.lua

## 📄 Licence

Vytvořeno pro FiveM komunitu. Volně použitelné a upravitelné.

---

**Autor**: valic  
**Verze**: 1.0.0  
**Kompatibilita**: QB-Core, QBox-Core
