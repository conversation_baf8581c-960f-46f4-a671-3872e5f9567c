* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
}

.hidden {
    display: none !important;
}

#wiring-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.wiring-panel {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border: 3px solid #444;
    border-radius: 15px;
    width: 800px;
    height: 600px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    position: relative;
}

.header {
    background: linear-gradient(145deg, #333, #222);
    border-radius: 12px 12px 0 0;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #444;
}

.title {
    color: #00ff88;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.timer {
    color: #ffaa00;
    font-size: 20px;
    font-weight: bold;
    background: rgba(255, 170, 0, 0.1);
    padding: 5px 15px;
    border-radius: 20px;
    border: 2px solid #ffaa00;
}

.close-btn {
    background: #ff4444;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #ff6666;
    transform: scale(1.1);
}

.game-area {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 20px;
    gap: 20px;
}

.connectors-left,
.connectors-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 120px;
}

.connector {
    width: 80px;
    height: 40px;
    border-radius: 20px;
    border: 3px solid #666;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.connector::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: currentColor;
    border: 2px solid #333;
}

.connector.left {
    margin-left: auto;
}

.connector.right {
    margin-right: auto;
}

.connector:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.connector.connected {
    border-color: #00ff88;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.connector.dragging {
    transform: scale(1.1);
    z-index: 1000;
}

.wires-area {
    flex: 1;
    position: relative;
    height: 100%;
}

#wires-svg {
    width: 100%;
    height: 100%;
}

.wire {
    stroke-width: 4;
    fill: none;
    stroke-linecap: round;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.wire.connected {
    opacity: 1;
    filter: drop-shadow(0 0 5px currentColor);
}

.progress-container {
    padding: 15px 20px;
    border-top: 2px solid #444;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #555;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #00cc66);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.progress-text {
    text-align: center;
    color: #ccc;
    margin-top: 8px;
    font-size: 14px;
}

.debug-info {
    position: absolute;
    top: 60px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff88;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    max-width: 200px;
    border: 1px solid #00ff88;
}

/* Animace */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.connector.pulse {
    animation: pulse 0.5s ease-in-out;
}

@keyframes success {
    0% { background: #00ff88; }
    100% { background: transparent; }
}

.success-flash {
    animation: success 0.5s ease-in-out;
}

@keyframes fail {
    0% { background: #ff4444; }
    100% { background: transparent; }
}

.fail-flash {
    animation: fail 0.5s ease-in-out;
}
