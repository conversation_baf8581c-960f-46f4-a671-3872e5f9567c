* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
    cursor: default !important;
}

html {
    cursor: default !important;
}

.hidden {
    display: none !important;
}

#wiring-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.wiring-panel {
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border: 4px solid #333;
    border-radius: 20px;
    width: 900px;
    height: 650px;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.header {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 16px 16px 0 0;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #333;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.title {
    color: #00ff88;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
    letter-spacing: 2px;
    text-transform: uppercase;
}

.timer {
    color: #ffaa00;
    font-size: 20px;
    font-weight: bold;
    background: rgba(255, 170, 0, 0.1);
    padding: 5px 15px;
    border-radius: 20px;
    border: 2px solid #ffaa00;
}

.close-btn {
    background: #ff4444;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #ff6666;
    transform: scale(1.1);
}

.game-area {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 30px;
    gap: 0;
    background: #000;
    margin: 10px;
    border-radius: 15px;
    border: 2px solid #333;
    position: relative;
}

.connectors-left,
.connectors-right {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 150px;
    padding: 20px 0;
}

.connector {
    width: 120px;
    height: 25px;
    border-radius: 0;
    border: none;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    background: currentColor;
    box-shadow:
        inset 0 3px 6px rgba(255, 255, 255, 0.3),
        inset 0 -3px 6px rgba(0, 0, 0, 0.5),
        0 2px 4px rgba(0, 0, 0, 0.8);
}

.connector.left {
    cursor: grab;
}

.connector.left:active {
    cursor: grabbing;
}

.connector.connected {
    cursor: default;
}

.connector::before {
    content: '';
    position: absolute;
    width: 15px;
    height: 15px;
    background: #222;
    border-radius: 50%;
    border: 2px solid #000;
    box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.connector.left {
    margin-left: auto;
    border-radius: 0 12px 12px 0;
}

.connector.left::before {
    right: 8px;
}

.connector.right {
    margin-right: auto;
    border-radius: 12px 0 0 12px;
}

.connector.right::before {
    left: 8px;
}

.connector:hover {
    transform: scale(1.02);
    filter: brightness(1.2);
    box-shadow:
        inset 0 3px 6px rgba(255, 255, 255, 0.4),
        inset 0 -3px 6px rgba(0, 0, 0, 0.5),
        0 2px 8px rgba(0, 0, 0, 0.9),
        0 0 15px rgba(255, 255, 255, 0.2);
}

.connector.connected {
    filter: brightness(1.3);
    box-shadow:
        inset 0 3px 6px rgba(255, 255, 255, 0.5),
        inset 0 -3px 6px rgba(0, 0, 0, 0.5),
        0 2px 8px rgba(0, 0, 0, 0.9),
        0 0 20px currentColor;
}

.connector.dragging {
    transform: scale(1.05);
    z-index: 1000;
    filter: brightness(1.4);
}

.wires-area {
    flex: 1;
    position: relative;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255,255,255,0.05) 0%,
        rgba(255,255,255,0.02) 50%,
        rgba(255,255,255,0.05) 100%);
    margin: 0 10px;
}

#wires-svg {
    width: 100%;
    height: 100%;
}

.wire {
    stroke-width: 8;
    fill: none;
    stroke-linecap: round;
    opacity: 0.9;
    transition: all 0.3s;
    filter: drop-shadow(0 0 8px currentColor);
}

.wire.connected {
    opacity: 1;
    stroke-width: 10;
    filter: drop-shadow(0 0 12px currentColor) drop-shadow(0 0 4px rgba(0,0,0,0.8));
}

.temp-wire {
    opacity: 0.7;
    stroke-width: 6;
    stroke-dasharray: 10,5;
    animation: dash 1s linear infinite;
    filter: drop-shadow(0 0 8px currentColor);
}

@keyframes dash {
    to {
        stroke-dashoffset: -15;
    }
}

.progress-container {
    padding: 15px 20px;
    border-top: 2px solid #444;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #555;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #00cc66);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.progress-text {
    text-align: center;
    color: #ccc;
    margin-top: 8px;
    font-size: 14px;
}



/* Animace */
@keyframes pulse {
    0% {
        transform: scale(1);
        filter: brightness(1);
        box-shadow:
            inset 0 3px 6px rgba(255, 255, 255, 0.3),
            inset 0 -3px 6px rgba(0, 0, 0, 0.5),
            0 2px 4px rgba(0, 0, 0, 0.8);
    }
    50% {
        transform: scale(1.08);
        filter: brightness(1.5);
        box-shadow:
            inset 0 3px 6px rgba(255, 255, 255, 0.6),
            inset 0 -3px 6px rgba(0, 0, 0, 0.5),
            0 2px 8px rgba(0, 0, 0, 0.9),
            0 0 25px currentColor;
    }
    100% {
        transform: scale(1);
        filter: brightness(1);
        box-shadow:
            inset 0 3px 6px rgba(255, 255, 255, 0.3),
            inset 0 -3px 6px rgba(0, 0, 0, 0.5),
            0 2px 4px rgba(0, 0, 0, 0.8);
    }
}

.connector.pulse {
    animation: pulse 0.6s ease-in-out infinite;
}

@keyframes success {
    0% { background: #00ff88; }
    100% { background: transparent; }
}

.success-flash {
    animation: success 0.5s ease-in-out;
}

@keyframes fail {
    0% { background: #ff4444; }
    100% { background: transparent; }
}

.fail-flash {
    animation: fail 0.5s ease-in-out;
}
