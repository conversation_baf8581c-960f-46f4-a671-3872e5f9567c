# Wiring Minigame pro FiveM (valic\_wiring-minigame)

## 1. <PERSON><PERSON><PERSON><PERSON>

Minigame umožňuje hráči propojovat kabely ve stylu "Among Us".

* Jazyk: **Lua** (qb-core / qbox-core)
* Aktivace: příkaz `/test_wiring`
* Debug mód (devmode) s výpisem přes `print`
* Timeout: nastavitelný (0 = nekonečno)
* Počet drátků: nastavitelný v configu
* Penalizace za chybný kabel: **celé kolo se resetuje**
* Zvuk: úspěch / neúspěch ve formátu `.ogg`

## 2. Konfigurace (`config.lua`)

```lua
Config = {
  TimeoutSeconds = 0,        -- 0 = nekonečno
  CableCount     = 5,        -- počet drátek v minihře
  DevMode        = true,     -- debug printy ON/OFF

  Colors = {                  -- 32 barev a jejich odstíny
    "#000000", "#00021c", "#1c284d", "#343473",
    "#2d5280", "#4d7a99", "#7497a6", "#a3ccd9",
    "#f0edd8", "#732866", "#a6216e", "#d94c87",
    "#d9214f", "#f25565", "#f27961", "#993649",
    "#b36159", "#f09c60", "#b38f24", "#b3b324",
    "#f7c93e", "#17735f", "#119955", "#67b31b",
    "#1ba683", "#47cca9", "#96e3c9", "#2469b3",
    "#0b8be6", "#0bafe6", "#f28d85", "#f0bb90",
  }
}
```

## 3. Složková struktura

```
valic_wiring-minigame/
├── fxmanifest.lua
├── config.lua
├── client/
│   └── main.lua        -- logika minigame + exporty
├── html/
│   ├── index.html      -- UI minihry
│   ├── style.css       -- vzhled podle zaslaného designu
│   └── script.js       -- komunikace s klientem
├── src/
│   ├── success.ogg
│   └── fail.ogg
└── README.md           -- stručná dokumentace
```

## 4. Exportované funkce & eventy

### Exporty (client/main.lua)

* `startWiring(playerId: number)`
* `stopWiring(playerId: number, success: boolean)`

### Eventy

* `valic:wiring:success` — spustí se zvuk úspěchu + pokračování
* `valic:wiring:fail`    — spustí se zvuk neúspěchu + reset minihry

## 5. Příkaz

```lua
-- client/main.lua
RegisterCommand('test_wiring', function()
  if Config.DevMode then print('[WIRING] Spouštím testovací mód') end
  exports['valic_wiring-minigame']:startWiring(GetPlayerServerId(PlayerId()))
end)
```

## 6. Průběh minihry

1. Hráči se zobrazí UI se dvěma řadami konektorů ve stylu obrázku.
2. Linie kabelů se zamíchají, hráč musí "táhnout" (drag) konektor stejné barvy na druhou stranu.
3. Po každém spojení:

   * Kontrola správnosti: pokud špatně ⇒ `fail`
   * Pokud správně a všechny hotové ⇒ `success`
4. Timeout (pokud > 0) po uplynutí času ⇒ `fail`
5. DevMode: `print` výpisy jednotlivých kroků

## 7. UI (html/index.html)

* Přizpůsobený styl dle zaslané grafiky
* Canvas nebo SVG pro linky kabelů
* Drag & drop konektory
* Timer + progress bar (volitelně)

## 8. Zvuky

Vložte .ogg soubory do `src/` a nahraďte placeholdery ve scriptu:

```lua
local S_SUCCESS = 'src/success.ogg'
local S_FAIL    = 'src/fail.ogg'
```

## 9. DevMode & Debug

* `Config.DevMode = true` ⇒ výpisy:

  * Start/stop minigry
  * Každý tah kabelu (barva, index)
  * Výsledek `success`/`fail`

## 10. Další kroky

1. Implementace `fxmanifest.lua` a načtení souborů.
2. Vyhotovení HTML/CSS podle designu.
3. Skript pro drag\&drop + validace v `script.js`.
4. Klientské volání exportů a zvuků v `main.lua`.
5. Testování v devmode `/test_wiring`.

---

*Autor: \[tvé\_jméno], datum*
