local isWiringActive = false
local currentGameData = {}

-- Debug print funkce
local function debugPrint(message)
    if Config.DevMode then
        print('[WIRING] ' .. message)
    end
end

-- Generování náhodných barev pro minigru
local function generateGameColors()
    local colors = {}
    local usedColors = {}
    
    for i = 1, Config.CableCount do
        local colorIndex
        repeat
            colorIndex = math.random(1, #Config.Colors)
        until not usedColors[colorIndex]
        
        usedColors[colorIndex] = true
        table.insert(colors, {
            index = i,
            color = Config.Colors[colorIndex],
            connected = false
        })
    end
    
    return colors
end

-- Spuštění wiring minigry
function startWiring(playerId)
    if isWiringActive then
        debugPrint('Wiring už je aktivní!')
        return false
    end
    
    debugPrint('Spouštím wiring minigru pro hráče: ' .. tostring(playerId))
    
    -- Generování dat pro hru
    currentGameData = {
        playerId = playerId,
        colors = generateGameColors(),
        startTime = GetGameTimer(),
        timeout = Config.TimeoutSeconds > 0 and (GetGameTimer() + (Config.TimeoutSeconds * 1000)) or nil
    }
    
    isWiringActive = true
    
    -- Odeslání dat do UI
    SendNUIMessage({
        type = 'startWiring',
        data = {
            colors = currentGameData.colors,
            timeout = Config.TimeoutSeconds,
            devMode = Config.DevMode
        }
    })
    
    -- Zobrazení UI
    SetNuiFocus(true, true)
    
    debugPrint('Wiring minihra spuštěna s ' .. Config.CableCount .. ' kabely')
    return true
end

-- Zastavení wiring minigry
function stopWiring(playerId, success)
    if not isWiringActive then
        debugPrint('Wiring není aktivní!')
        return false
    end
    
    debugPrint('Zastavuji wiring minigru - úspěch: ' .. tostring(success))
    
    isWiringActive = false
    currentGameData = {}
    
    -- Skrytí UI
    SetNuiFocus(false, false)
    
    -- Odeslání stop zprávy do UI
    SendNUIMessage({
        type = 'stopWiring'
    })
    
    -- Spuštění příslušného eventu
    if success then
        TriggerEvent('valic:wiring:success', playerId)
        debugPrint('Wiring dokončen úspěšně!')
    else
        TriggerEvent('valic:wiring:fail', playerId)
        debugPrint('Wiring selhal!')
    end
    
    return true
end

-- Exporty
exports('startWiring', startWiring)
exports('stopWiring', stopWiring)

-- NUI Callbacks
RegisterNUICallback('wiringComplete', function(data, cb)
    debugPrint('Wiring dokončen - úspěch: ' .. tostring(data.success))
    
    if data.success then
        -- Přehrání zvuku úspěchu
        SendNUIMessage({
            type = 'playSound',
            sound = 'success'
        })
        stopWiring(currentGameData.playerId, true)
    else
        -- Přehrání zvuku neúspěchu
        SendNUIMessage({
            type = 'playSound',
            sound = 'fail'
        })
        stopWiring(currentGameData.playerId, false)
    end
    
    cb('ok')
end)

RegisterNUICallback('wiringFailed', function(data, cb)
    debugPrint('Wiring selhal - důvod: ' .. tostring(data.reason))
    
    -- Přehrání zvuku neúspěchu
    SendNUIMessage({
        type = 'playSound',
        sound = 'fail'
    })
    
    stopWiring(currentGameData.playerId, false)
    cb('ok')
end)

RegisterNUICallback('closeUI', function(data, cb)
    debugPrint('UI zavřeno uživatelem')
    stopWiring(currentGameData.playerId, false)
    cb('ok')
end)

-- Eventy
RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    debugPrint('Event: Wiring úspěch pro hráče ' .. tostring(playerId))
    -- Zde můžete přidat další logiku pro úspěšné dokončení
end)

RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    debugPrint('Event: Wiring neúspěch pro hráče ' .. tostring(playerId))
    -- Zde můžete přidat další logiku pro neúspěšné dokončení
end)

-- Testovací příkaz
RegisterCommand('test_wiring', function()
    debugPrint('Spouštím testovací mód wiring minigry')
    startWiring(GetPlayerServerId(PlayerId()))
end)

-- Timeout kontrola
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        if isWiringActive and currentGameData.timeout then
            if GetGameTimer() >= currentGameData.timeout then
                debugPrint('Wiring timeout!')
                stopWiring(currentGameData.playerId, false)
            end
        end
    end
end)

-- ========================================
-- KONFIGURAČNÍ PŘÍKAZY
-- ========================================

-- Nastavení timeoutu
RegisterCommand('wiring_timeout', function(source, args)
    if not args[1] then
        debugPrint('Použití: /wiring_timeout <sekundy> (0 = nekonečno)')
        return
    end

    local seconds = tonumber(args[1])
    if not seconds or seconds < 0 then
        debugPrint('Chyba: Zadejte platné číslo sekund (0 nebo více)')
        return
    end

    Config.TimeoutSeconds = seconds
    if seconds == 0 then
        debugPrint('Timeout nastaven na: NEKONEČNO')
    else
        debugPrint('Timeout nastaven na: ' .. seconds .. ' sekund')
    end
end)

-- Nastavení počtu drátků
RegisterCommand('wiring_cables', function(source, args)
    if not args[1] then
        debugPrint('Použití: /wiring_cables <počet> (3-10 doporučeno)')
        return
    end

    local count = tonumber(args[1])
    if not count or count < 1 or count > 32 then
        debugPrint('Chyba: Počet drátků musí být mezi 1-32')
        return
    end

    Config.CableCount = count
    debugPrint('Počet drátků nastaven na: ' .. count)
end)

-- Přepnutí dev módu
RegisterCommand('wiring_devmode', function(source, args)
    Config.DevMode = not Config.DevMode
    debugPrint('Dev mód: ' .. (Config.DevMode and 'ZAPNUT' or 'VYPNUT'))
end)

-- Zobrazení aktuální konfigurace
RegisterCommand('wiring_config', function(source, args)
    debugPrint('=== WIRING KONFIGURACE ===')
    debugPrint('Timeout: ' .. (Config.TimeoutSeconds == 0 and 'NEKONEČNO' or Config.TimeoutSeconds .. ' sekund'))
    debugPrint('Počet drátků: ' .. Config.CableCount)
    debugPrint('Dev mód: ' .. (Config.DevMode and 'ZAPNUT' or 'VYPNUT'))
    debugPrint('Počet dostupných barev: ' .. #Config.Colors)
end)

-- Spuštění s vlastními parametry
RegisterCommand('wiring_custom', function(source, args)
    if not args[1] or not args[2] then
        debugPrint('Použití: /wiring_custom <timeout> <počet_drátků>')
        debugPrint('Příklad: /wiring_custom 60 7 (60 sekund, 7 drátků)')
        return
    end

    local timeout = tonumber(args[1])
    local cables = tonumber(args[2])

    if not timeout or timeout < 0 then
        debugPrint('Chyba: Neplatný timeout')
        return
    end

    if not cables or cables < 1 or cables > 32 then
        debugPrint('Chyba: Počet drátků musí být mezi 1-32')
        return
    end

    -- Dočasné uložení původní konfigurace
    local originalTimeout = Config.TimeoutSeconds
    local originalCables = Config.CableCount

    -- Nastavení vlastních hodnot
    Config.TimeoutSeconds = timeout
    Config.CableCount = cables

    debugPrint('Spouštím custom wiring: ' .. timeout .. 's, ' .. cables .. ' drátků')

    -- Spuštění hry
    local success = startWiring(GetPlayerServerId(PlayerId()))

    if not success then
        -- Obnovení původní konfigurace při chybě
        Config.TimeoutSeconds = originalTimeout
        Config.CableCount = originalCables
    end
end)

-- Reset konfigurace na výchozí hodnoty
RegisterCommand('wiring_reset', function(source, args)
    Config.TimeoutSeconds = 0
    Config.CableCount = 5
    Config.DevMode = true
    debugPrint('Konfigurace resetována na výchozí hodnoty')
    debugPrint('Timeout: NEKONEČNO, Drátky: 5, Dev mód: ZAPNUT')
end)
