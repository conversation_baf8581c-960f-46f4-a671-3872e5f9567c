local isWiringActive = false
local currentGameData = {}

-- Debug print funkce
local function debugPrint(message)
    if Config.DevMode then
        print('[WIRING] ' .. message)
    end
end

-- Generování náhodných barev pro minigru
local function generateGameColors()
    local colors = {}
    local usedColors = {}
    
    for i = 1, Config.CableCount do
        local colorIndex
        repeat
            colorIndex = math.random(1, #Config.Colors)
        until not usedColors[colorIndex]
        
        usedColors[colorIndex] = true
        table.insert(colors, {
            index = i,
            color = Config.Colors[colorIndex],
            connected = false
        })
    end
    
    return colors
end

-- Spuštění wiring minigry
function startWiring(playerId)
    if isWiringActive then
        debugPrint('Wiring už je aktivní!')
        return false
    end
    
    debugPrint('Spouštím wiring minigru pro hráče: ' .. tostring(playerId))
    
    -- Generování dat pro hru
    currentGameData = {
        playerId = playerId,
        colors = generateGameColors(),
        startTime = GetGameTimer(),
        timeout = Config.TimeoutSeconds > 0 and (GetGameTimer() + (Config.TimeoutSeconds * 1000)) or nil
    }
    
    isWiringActive = true
    
    -- Odeslání dat do UI
    SendNUIMessage({
        type = 'startWiring',
        data = {
            colors = currentGameData.colors,
            timeout = Config.TimeoutSeconds,
            devMode = Config.DevMode
        }
    })
    
    -- Zobrazení UI
    SetNuiFocus(true, true)
    
    debugPrint('Wiring minihra spuštěna s ' .. Config.CableCount .. ' kabely')
    return true
end

-- Zastavení wiring minigry
function stopWiring(playerId, success)
    if not isWiringActive then
        debugPrint('Wiring není aktivní!')
        return false
    end
    
    debugPrint('Zastavuji wiring minigru - úspěch: ' .. tostring(success))
    
    isWiringActive = false
    currentGameData = {}
    
    -- Skrytí UI
    SetNuiFocus(false, false)
    
    -- Odeslání stop zprávy do UI
    SendNUIMessage({
        type = 'stopWiring'
    })
    
    -- Spuštění příslušného eventu
    if success then
        TriggerEvent('valic:wiring:success', playerId)
        debugPrint('Wiring dokončen úspěšně!')
    else
        TriggerEvent('valic:wiring:fail', playerId)
        debugPrint('Wiring selhal!')
    end
    
    return true
end

-- Exporty
exports('startWiring', startWiring)
exports('stopWiring', stopWiring)

-- NUI Callbacks
RegisterNUICallback('wiringComplete', function(data, cb)
    debugPrint('Wiring dokončen - úspěch: ' .. tostring(data.success))
    
    if data.success then
        -- Přehrání zvuku úspěchu
        SendNUIMessage({
            type = 'playSound',
            sound = 'success'
        })
        stopWiring(currentGameData.playerId, true)
    else
        -- Přehrání zvuku neúspěchu
        SendNUIMessage({
            type = 'playSound',
            sound = 'fail'
        })
        stopWiring(currentGameData.playerId, false)
    end
    
    cb('ok')
end)

RegisterNUICallback('wiringFailed', function(data, cb)
    debugPrint('Wiring selhal - důvod: ' .. tostring(data.reason))
    
    -- Přehrání zvuku neúspěchu
    SendNUIMessage({
        type = 'playSound',
        sound = 'fail'
    })
    
    stopWiring(currentGameData.playerId, false)
    cb('ok')
end)

RegisterNUICallback('closeUI', function(data, cb)
    debugPrint('UI zavřeno uživatelem')
    stopWiring(currentGameData.playerId, false)
    cb('ok')
end)

-- Eventy
RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    debugPrint('Event: Wiring úspěch pro hráče ' .. tostring(playerId))
    -- Zde můžete přidat další logiku pro úspěšné dokončení
end)

RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    debugPrint('Event: Wiring neúspěch pro hráče ' .. tostring(playerId))
    -- Zde můžete přidat další logiku pro neúspěšné dokončení
end)

-- Testovací příkaz
RegisterCommand('test_wiring', function()
    debugPrint('Spouštím testovací mód wiring minigry')
    startWiring(GetPlayerServerId(PlayerId()))
end)

-- Timeout kontrola
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        
        if isWiringActive and currentGameData.timeout then
            if GetGameTimer() >= currentGameData.timeout then
                debugPrint('Wiring timeout!')
                stopWiring(currentGameData.playerId, false)
            end
        end
    end
end)
