class WiringMinigame {
    constructor() {
        this.gameData = null;
        this.connections = [];
        this.isActive = false;
        this.devMode = false;
        this.timer = null;
        this.timeLeft = 0;
        this.draggedConnector = null;

        this.initializeElements();
        this.setupEventListeners();
    }
    
    initializeElements() {
        this.container = document.getElementById('wiring-container');
        this.leftConnectors = document.getElementById('left-connectors');
        this.rightConnectors = document.getElementById('right-connectors');
        this.wiresSvg = document.getElementById('wires-svg');
        this.timerElement = document.getElementById('timer');
        this.progressFill = document.getElementById('progress-fill');
        this.connectedCount = document.getElementById('connected-count');
        this.totalCount = document.getElementById('total-count');
        // Debug elementy odstraněny
        this.closeBtn = document.getElementById('close-btn');
        this.successSound = document.getElementById('success-sound');
        this.failSound = document.getElementById('fail-sound');
    }
    
    setupEventListeners() {
        this.closeBtn.addEventListener('click', () => this.closeGame());

        // ESC klávesa pro zavření
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isActive) {
                this.closeGame();
            }
        });

        // NUI komunikace
        window.addEventListener('message', (event) => {
            const data = event.data;

            switch(data.type) {
                case 'startWiring':
                    this.startGame(data.data);
                    break;
                case 'stopWiring':
                    this.stopGame();
                    break;
                case 'playSound':
                    this.playSound(data.sound);
                    break;
            }
        });
    }
    
    startGame(data) {
        this.gameData = data;
        this.devMode = data.devMode;
        this.isActive = true;
        this.connections = [];
        
        this.debugLog('Spouštím wiring minigru');
        
        // Nastavení UI
        this.setupUI();
        this.generateConnectors();
        this.updateProgress();
        
        // Spuštění timeru
        if (data.timeout > 0) {
            this.startTimer(data.timeout);
        }
        
        // Zobrazení hry
        this.container.classList.remove('hidden');
    }
    
    stopGame() {
        this.isActive = false;
        this.container.classList.add('hidden');

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        // Reset kurzoru a drag stavu
        this.resetDragState();

        this.debugLog('Hra zastavena');
    }

    resetDragState() {
        if (this.draggedConnector) {
            this.draggedConnector.classList.remove('dragging');
            this.draggedConnector = null;
        }

        // Odstranit všechny pulse efekty
        document.querySelectorAll('.connector.pulse').forEach(el => {
            el.classList.remove('pulse');
        });

        // Odstranit dočasný kabel
        this.removeTempWire();

        // Reset kurzoru
        document.body.style.cursor = '';

        // Odstranit global listenery
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleGlobalMouseUp.bind(this));
    }
    
    setupUI() {
        this.totalCount.textContent = this.gameData.colors.length;
        this.connectedCount.textContent = '0';
        this.progressFill.style.width = '0%';
        
        // Vyčištění předchozích konektorů a kabelů
        this.leftConnectors.innerHTML = '';
        this.rightConnectors.innerHTML = '';
        this.wiresSvg.innerHTML = '';
    }
    
    generateConnectors() {
        const colors = [...this.gameData.colors];
        const rightColors = [...colors].sort(() => Math.random() - 0.5); // Zamíchání pravé strany
        
        // Levé konektory (v pořadí)
        colors.forEach((colorData, index) => {
            const connector = this.createConnector(colorData, 'left', index);
            this.leftConnectors.appendChild(connector);
        });
        
        // Pravé konektory (zamíchané)
        rightColors.forEach((colorData, index) => {
            const connector = this.createConnector(colorData, 'right', index);
            this.rightConnectors.appendChild(connector);
        });
    }
    
    createConnector(colorData, side, index) {
        const connector = document.createElement('div');
        connector.className = `connector ${side}`;
        connector.style.color = colorData.color;
        connector.dataset.colorIndex = colorData.index;
        connector.dataset.side = side;
        connector.dataset.position = index;
        
        // Click & Drag pro levé konektory
        if (side === 'left') {
            connector.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        } else {
            // Mouse eventy pro pravé konektory
            connector.addEventListener('mouseup', (e) => this.handleMouseUp(e));
            connector.addEventListener('mouseenter', (e) => this.handleMouseEnter(e));
            connector.addEventListener('mouseleave', (e) => this.handleMouseLeave(e));
        }
        
        return connector;
    }
    
    // HTML5 drag & drop handlery odstraněny - používáme pouze click & drag
    
    makeConnection(colorIndex, targetConnector) {
        // Najdeme zdrojový konektor
        const sourceConnector = document.querySelector(`[data-color-index="${colorIndex}"][data-side="left"]`);

        // Kontrola, zda už není spojen
        if (sourceConnector.classList.contains('connected')) {
            this.debugLog(`Kabel ${colorIndex} už je spojen`);
            return;
        }

        // Označíme jako spojené
        sourceConnector.classList.add('connected');
        targetConnector.classList.add('connected');

        // Zakážeme další drag
        sourceConnector.draggable = false;
        
        // Přidáme spojení
        this.connections.push({
            colorIndex: colorIndex,
            source: sourceConnector,
            target: targetConnector
        });
        
        // Vykreslíme kabel
        this.drawWire(sourceConnector, targetConnector, this.getColorByIndex(colorIndex));
        
        this.debugLog(`Úspěšné spojení kabelu ${colorIndex}`);
        
        // Aktualizace progress
        this.updateProgress();
        
        // Kontrola dokončení
        if (this.connections.length === this.gameData.colors.length) {
            this.handleGameComplete(true);
        }
    }
    
    handleWrongConnection() {
        this.debugLog('Špatné spojení - resetuji hru');
        
        // Flash efekt
        document.body.classList.add('fail-flash');
        setTimeout(() => document.body.classList.remove('fail-flash'), 500);
        
        // Reset všech spojení
        this.resetConnections();
        
        // Oznámení o selhání
        this.handleGameComplete(false);
    }
    
    resetConnections() {
        // Odstranění všech spojení
        this.connections = [];
        
        // Reset konektorů
        document.querySelectorAll('.connector').forEach(connector => {
            connector.classList.remove('connected');
            if (connector.dataset.side === 'left') {
                connector.draggable = true;
            }
        });
        
        // Vyčištění kabelů
        this.wiresSvg.innerHTML = '';
        
        // Reset progress
        this.updateProgress();
    }
    
    drawWire(sourceConnector, targetConnector, color) {
        const sourceRect = sourceConnector.getBoundingClientRect();
        const targetRect = targetConnector.getBoundingClientRect();
        const svgRect = this.wiresSvg.getBoundingClientRect();
        
        const startX = sourceRect.right - svgRect.left;
        const startY = sourceRect.top + sourceRect.height / 2 - svgRect.top;
        const endX = targetRect.left - svgRect.left;
        const endY = targetRect.top + targetRect.height / 2 - svgRect.top;
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const controlX1 = startX + (endX - startX) * 0.3;
        const controlX2 = startX + (endX - startX) * 0.7;
        
        const d = `M ${startX} ${startY} C ${controlX1} ${startY} ${controlX2} ${endY} ${endX} ${endY}`;
        
        path.setAttribute('d', d);
        path.setAttribute('stroke', color);
        path.classList.add('wire', 'connected');
        
        this.wiresSvg.appendChild(path);
    }
    
    getColorByIndex(index) {
        return this.gameData.colors.find(c => c.index == index)?.color || '#ffffff';
    }
    
    updateProgress() {
        const progress = (this.connections.length / this.gameData.colors.length) * 100;
        this.progressFill.style.width = `${progress}%`;
        this.connectedCount.textContent = this.connections.length;
    }
    
    handleGameComplete(success) {
        this.isActive = false;

        if (success) {
            this.debugLog('Hra dokončena úspěšně!');
            document.body.classList.add('success-flash');
            setTimeout(() => document.body.classList.remove('success-flash'), 500);
        } else {
            this.debugLog('Hra selhala!');
        }

        // Odeslání výsledku do Lua
        fetch(`https://${GetParentResourceName()}/wiringComplete`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ success: success })
        });

        // Automatické zavření po krátké pauze
        setTimeout(() => {
            this.stopGame();
        }, success ? 1500 : 1000); // 1.5s pro úspěch, 1s pro fail
    }
    
    startTimer(seconds) {
        this.timeLeft = seconds;
        this.updateTimer();
        
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.updateTimer();
            
            if (this.timeLeft <= 0) {
                this.handleTimeout();
            }
        }, 1000);
    }
    
    updateTimer() {
        if (this.timeLeft <= 0) {
            this.timerElement.textContent = '0:00';
            this.timerElement.style.color = '#ff4444';
        } else {
            const minutes = Math.floor(this.timeLeft / 60);
            const seconds = this.timeLeft % 60;
            this.timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            if (this.timeLeft <= 10) {
                this.timerElement.style.color = '#ff4444';
            }
        }
    }
    
    handleTimeout() {
        this.debugLog('Čas vypršel!');
        clearInterval(this.timer);
        this.timer = null;
        
        fetch(`https://${GetParentResourceName()}/wiringFailed`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ reason: 'timeout' })
        });
    }
    
    closeGame() {
        fetch(`https://${GetParentResourceName()}/closeUI`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
    
    playSound(soundType) {
        if (soundType === 'success') {
            this.successSound.play().catch(e => console.log('Nelze přehrát zvuk úspěchu'));
        } else if (soundType === 'fail') {
            this.failSound.play().catch(e => console.log('Nelze přehrát zvuk selhání'));
        }
    }
    
    debugLog(message) {
        if (this.devMode) {
            console.log('[WIRING] ' + message);
        }
    }

    // Alternativní mouse eventy pro FiveM (pokud drag & drop nefunguje)
    handleMouseDown(e) {
        if (!this.isActive) return;

        const connector = e.target;
        if (connector.classList.contains('connected')) return;

        this.draggedConnector = connector;
        connector.classList.add('dragging');

        // Nastavit kurzor
        document.body.style.cursor = 'grabbing';

        this.debugLog(`Mouse down na kabel ${connector.dataset.colorIndex}`);

        // Přidáme global mouse move a mouse up
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleGlobalMouseUp.bind(this));
    }

    handleMouseMove(e) {
        if (!this.draggedConnector) return;

        // Vykreslení dočasného kabelu
        this.drawTempWire(this.draggedConnector, e.clientX, e.clientY);
    }

    handleGlobalMouseUp(e) {
        if (this.draggedConnector) {
            this.draggedConnector.classList.remove('dragging');
            this.draggedConnector = null;
        }

        // Odstranit dočasný kabel
        this.removeTempWire();

        // Odstranit všechny pulse efekty
        document.querySelectorAll('.connector.pulse').forEach(el => {
            el.classList.remove('pulse');
        });

        // Reset kurzoru
        document.body.style.cursor = '';

        // Odstranit global listenery
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleGlobalMouseUp.bind(this));
    }

    handleMouseUp(e) {
        if (!this.draggedConnector || !this.isActive) return;

        const targetConnector = e.target;
        if (!targetConnector.classList.contains('connector') || targetConnector.dataset.side !== 'right') {
            // Reset stavu pokud není platný cíl
            this.resetDragState();
            return;
        }

        if (targetConnector.classList.contains('connected')) {
            this.debugLog('Cílový konektor už je spojen (mouse)');
            this.resetDragState();
            return;
        }

        const draggedColorIndex = this.draggedConnector.dataset.colorIndex;
        const targetColorIndex = targetConnector.dataset.colorIndex;

        this.debugLog(`Mouse spojení: ${draggedColorIndex} -> ${targetColorIndex}`);

        // Reset drag stavu před zpracováním
        this.resetDragState();

        // Kontrola správnosti spojení
        if (draggedColorIndex === targetColorIndex) {
            this.makeConnection(draggedColorIndex, targetConnector);
        } else {
            this.handleWrongConnection();
        }
    }

    handleMouseEnter(e) {
        if (this.draggedConnector && e.target.classList.contains('connector') && e.target.dataset.side === 'right') {
            e.target.classList.add('pulse');
        }
    }

    handleMouseLeave(e) {
        if (e.target.classList.contains('connector')) {
            e.target.classList.remove('pulse');
        }
    }

    // Funkce pro kreslení dočasného kabelu při tažení
    drawTempWire(sourceConnector, mouseX, mouseY) {
        // Odstranit předchozí dočasný kabel
        this.removeTempWire();

        const sourceRect = sourceConnector.getBoundingClientRect();
        const svgRect = this.wiresSvg.getBoundingClientRect();

        const startX = sourceRect.right - svgRect.left;
        const startY = sourceRect.top + sourceRect.height / 2 - svgRect.top;
        const endX = mouseX - svgRect.left;
        const endY = mouseY - svgRect.top;

        // Kontrola, zda je myš v oblasti SVG
        if (endX < 0 || endX > svgRect.width || endY < 0 || endY > svgRect.height) {
            return;
        }

        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const controlX1 = startX + (endX - startX) * 0.3;
        const controlX2 = startX + (endX - startX) * 0.7;

        const d = `M ${startX} ${startY} C ${controlX1} ${startY} ${controlX2} ${endY} ${endX} ${endY}`;

        path.setAttribute('d', d);
        path.setAttribute('stroke', this.getColorByIndex(sourceConnector.dataset.colorIndex));
        path.setAttribute('stroke-width', '6');
        path.setAttribute('fill', 'none');
        path.setAttribute('stroke-linecap', 'round');
        path.setAttribute('opacity', '0.7');
        path.setAttribute('stroke-dasharray', '10,5');
        path.classList.add('temp-wire');

        this.wiresSvg.appendChild(path);
    }

    removeTempWire() {
        const tempWire = this.wiresSvg.querySelector('.temp-wire');
        if (tempWire) {
            tempWire.remove();
        }
    }
}

// Inicializace hry
const wiringGame = new WiringMinigame();

// Utility funkce pro FiveM
function GetParentResourceName() {
    return window.location.hostname;
}
