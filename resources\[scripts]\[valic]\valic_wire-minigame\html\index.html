<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wiring Minigame</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="wiring-container" class="hidden">
        <div class="wiring-panel">
            <!-- Header s timerem -->
            <div class="header">
                <div class="title">WIRING TASK</div>
                <div id="timer" class="timer">∞</div>
                <button id="close-btn" class="close-btn">×</button>
            </div>
            
            <!-- Hlavn<PERSON> herní oblast -->
            <div class="game-area">
                <!-- Levá strana - výchozí konektory -->
                <div class="connectors-left" id="left-connectors">
                    <!-- Konektory se vygeneruj<PERSON> dynamicky -->
                </div>
                
                <!-- Střední oblast pro kabely -->
                <div class="wires-area">
                    <svg id="wires-svg" width="300" height="400">
                        <!-- Kabely se vykreslí zde -->
                    </svg>
                </div>
                
                <!-- Pravá strana - cílové konektory -->
                <div class="connectors-right" id="right-connectors">
                    <!-- Konektory se vygenerují dynamicky -->
                </div>
            </div>
            
            <!-- Progress bar -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <div class="progress-text">
                    <span id="connected-count">0</span> / <span id="total-count">0</span> connected
                </div>
            </div>
            

        </div>
    </div>
    
    <!-- Zvukové elementy -->
    <audio id="success-sound" preload="auto">
        <source src="src/success.ogg" type="audio/ogg">
    </audio>
    
    <audio id="fail-sound" preload="auto">
        <source src="src/fail.ogg" type="audio/ogg">
    </audio>
    
    <script src="script.js"></script>
</body>
</html>
