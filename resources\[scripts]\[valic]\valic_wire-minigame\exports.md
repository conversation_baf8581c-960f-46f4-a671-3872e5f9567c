# Valic Wiring Minigame - Exporty a API

Kompletní dokumentace všech exportů, eventů a příkazů pro wiring minigame.

## 📋 Obsah

- [Exporty](#exporty)
- [Eventy](#eventy)
- [Příkazy](#příkazy)
- [Příklady použití](#příklady-použití)

---

## 🔗 Exporty

### `startWiring(playerId)`

Spustí wiring minigru pro zadaného hráče.

**Parametry:**
- `playerId` (number) - Server ID hráče

**Návratová hodnota:**
- `boolean` - `true` při úspě<PERSON>ném spuštění, `false` při chybě

**Příklad:**
```lua
local playerId = GetPlayerServerId(PlayerId())
local success = exports['valic_wire-minigame']:startWiring(playerId)

if success then
    print('Wiring minihra spuštěna')
else
    print('Chyba při spouštění - minihra už běží')
end
```

### `stopWiring(playerId, success)`

Zastaví wiring minigru pro zadaného hráče.

**Parametry:**
- `playerId` (number) - Server ID hráče
- `success` (boolean) - Zda byla hra dokončena úspěšně

**Návratová hodnota:**
- `boolean` - `true` při úspěšném zastavení, `false` při chybě

**Příklad:**
```lua
local playerId = GetPlayerServerId(PlayerId())
local success = exports['valic_wire-minigame']:stopWiring(playerId, true)

if success then
    print('Wiring minihra zastavena')
end
```

---

## 📡 Eventy

### `valic:wiring:success`

Spustí se při úspěšném dokončení wiring minigry.

**Parametry:**
- `playerId` (number) - Server ID hráče

**Příklad:**
```lua
RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    local player = GetPlayerFromServerId(playerId)
    if player then
        -- Odměna za úspěšné dokončení
        TriggerClientEvent('QBCore:Notify', player, 'Wiring dokončen!', 'success')
        -- Přidat peníze, XP, atd.
    end
end)
```

### `valic:wiring:fail`

Spustí se při neúspěšném dokončení wiring minigry.

**Parametry:**
- `playerId` (number) - Server ID hráče

**Příklad:**
```lua
RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    local player = GetPlayerFromServerId(playerId)
    if player then
        -- Penalizace za neúspěch
        TriggerClientEvent('QBCore:Notify', player, 'Wiring selhal!', 'error')
        -- Odečíst životy, čas, atd.
    end
end)
```

---

## 🎮 Příkazy

### Testovací příkazy

#### `/test_wiring`
Spustí testovací wiring minigru s aktuální konfigurací.

### Konfigurační příkazy

#### `/wiring_timeout <sekundy>`
Nastaví timeout pro wiring minigru.

**Parametry:**
- `sekundy` (number) - Počet sekund (0 = nekonečno)

**Příklady:**
```
/wiring_timeout 60    # 60 sekund
/wiring_timeout 0     # Nekonečno
```

#### `/wiring_cables <počet>`
Nastaví počet drátků v minihře.

**Parametry:**
- `počet` (number) - Počet drátků (1-32)

**Příklady:**
```
/wiring_cables 5      # 5 drátků
/wiring_cables 10     # 10 drátků
```

#### `/wiring_devmode`
Přepne debug mód (zapne/vypne).

#### `/wiring_config`
Zobrazí aktuální konfiguraci.

#### `/wiring_custom <timeout> <počet_drátků>`
Spustí wiring s vlastními parametry.

**Příklady:**
```
/wiring_custom 30 7   # 30 sekund, 7 drátků
/wiring_custom 0 3    # Nekonečno, 3 drátky
```

#### `/wiring_reset`
Resetuje konfiguraci na výchozí hodnoty.

---

## 💡 Příklady použití

### Základní integrace

```lua
-- client.lua vašeho scriptu

local function startWiringTask()
    local playerId = GetPlayerServerId(PlayerId())
    local success = exports['valic_wire-minigame']:startWiring(playerId)
    
    if not success then
        QBCore.Functions.Notify('Wiring úkol už běží!', 'error')
    end
end

-- Event handlery
RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    -- Úspěšné dokončení
    QBCore.Functions.Notify('Wiring dokončen úspěšně!', 'success')
    -- Pokračovat s dalším úkolem
end)

RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    -- Neúspěšné dokončení
    QBCore.Functions.Notify('Wiring selhal! Zkuste to znovu.', 'error')
    -- Možnost opakování
end)
```

### Pokročilá integrace s konfigurací

```lua
-- Nastavení vlastní konfigurace před spuštěním
local function startCustomWiring(timeout, cableCount)
    -- Dočasné uložení původní konfigurace
    local originalTimeout = Config.TimeoutSeconds
    local originalCables = Config.CableCount
    
    -- Nastavení vlastních hodnot
    Config.TimeoutSeconds = timeout
    Config.CableCount = cableCount
    
    local playerId = GetPlayerServerId(PlayerId())
    local success = exports['valic_wire-minigame']:startWiring(playerId)
    
    if not success then
        -- Obnovení původní konfigurace při chybě
        Config.TimeoutSeconds = originalTimeout
        Config.CableCount = originalCables
    end
    
    return success
end

-- Použití
startCustomWiring(45, 6) -- 45 sekund, 6 drátků
```

### Integrace s QB-Core úkoly

```lua
-- server.lua
QBCore.Functions.CreateCallback('wiring:start', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false) end
    
    -- Kontrola podmínek (má nástroje, je na správném místě, atd.)
    if hasRequiredItems(Player) then
        TriggerClientEvent('wiring:startMinigame', source)
        cb(true)
    else
        cb(false)
    end
end)

-- client.lua
RegisterNetEvent('wiring:startMinigame')
AddEventHandler('wiring:startMinigame', function()
    local playerId = GetPlayerServerId(PlayerId())
    exports['valic_wire-minigame']:startWiring(playerId)
end)

RegisterNetEvent('valic:wiring:success')
AddEventHandler('valic:wiring:success', function(playerId)
    -- Oznámení serveru o úspěchu
    TriggerServerEvent('wiring:completed', true)
end)

RegisterNetEvent('valic:wiring:fail')
AddEventHandler('valic:wiring:fail', function(playerId)
    -- Oznámení serveru o neúspěchu
    TriggerServerEvent('wiring:completed', false)
end)
```

### Integrace s progress bary

```lua
local function startWiringWithProgress()
    QBCore.Functions.Progressbar('wiring_setup', 'Připravuji wiring...', 2000, false, true, {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
    }, {}, {}, {}, function() -- Done
        local playerId = GetPlayerServerId(PlayerId())
        exports['valic_wire-minigame']:startWiring(playerId)
    end, function() -- Cancel
        QBCore.Functions.Notify('Wiring zrušen', 'error')
    end)
end
```

---

## ⚙️ Konfigurace

Všechny konfigurační hodnoty lze měnit za běhu pomocí příkazů nebo přímo v kódu:

```lua
-- Přístup ke konfiguraci
Config.TimeoutSeconds = 30    -- 30 sekund timeout
Config.CableCount = 8         -- 8 drátků
Config.DevMode = false        -- Vypnout debug
```

---

## 🐛 Řešení problémů

### Časté chyby

1. **Export neexistuje**: Ujistěte se, že je resource spuštěn
2. **Minihra se nespustí**: Zkontrolujte, zda už neběží jiná instance
3. **Eventy se nespouštějí**: Ověřte správnou registraci event handlerů

### Debug

Zapněte dev mód pro detailní výpisy:
```
/wiring_devmode
```

---

**Verze**: 1.0.0  
**Kompatibilita**: QB-Core, QBox-Core  
**Autor**: valic
