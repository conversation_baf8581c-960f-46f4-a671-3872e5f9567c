udělej mi plan.md na to abych do fivem pro qbcore a qbox core ud<PERSON><PERSON> wiring minihru kde se budou muset propojovat káblíky, bude se to testovat skrez command a bude to stylem jako v among us, bude se moci nastavit čas jak dlouho ta minihra pojede, kolik tam budfe dr<PERSON>t<PERSON> (v<PERSON><PERSON> musí být na jedné straně jeden odstín barvy a na druhé stejný aby se to dalo propojit a bude tam třeba v confoigu 32 základních barev a jejich případných odstínů ale aby se to dalo lidským okem odlišit) timer bude defautlně off takže se do exportu a jeho proměné dá 0 to bude znamenat nekonečno sekund, bude to vše skrze exporty a ať k tomu je řádná dokumentace, UI bude v html, kdyby si mě l nějaké otázky tak se ptej a pak uděláme plan.md, na konci toho pokud to bude dobře tak se rozezn<PERSON> jeden sound a pokud to bude špaatné tak se ozve druhý sound

ChatGPT řekl:

Přemýšlení několik sekund
Abychom probrali všechny detaily a mohl sestavit opravdu kompletní plan.md, mám na tebe pár otázek:

Spouštění a lokace minihry

Bude se wiring spouštět výhradně příkazem (např. /wiring), nebo ho chceme navázat i na nějakou interakci v prostředí (dvorklik na skříň, tlakové body apod.)?

Parametry konfigurace

Kromě počtu drátků (např. 3–10) a timeoutu (v sekundách nebo 0 = neomezeno), chceš v configu i nastavit třeba penalizaci za špatné připojení (odčítání času / životů)?

Barevná paleta

Říkal jsi 32 základních barev a jejich odstínů — máš už seznam těch 32 barev? Nebo ho mám navrhnout já?

UI / HTML design

Máš představu, jestli chceme čisté CSS, Bootstrap/Tailwind, nebo vlastní styl?

Kromě samotných drátků bude v dialogu i progress bar, tlačítko na restart, počet chyb?

Zvuky

Budeš mít vlastní .ogg/.wav soubory, nebo je máme osadit placeholder názvy a cestu k nim?

Dokumentace & exporty

Chceš k exportům (funkce jako startWiring, stopWiring, eventy na success/fail) dodat i TypeScript deklarace, nebo stačí čisté Lua komentáře?

Když na to odpovíš, sepíšu ti kompletní plan.md s rozvržením složek, configu, exportů, UI a ukázkovými kódy. 🙂







Řekl/a jsi:
testovaci jako v devmode tak tam budou debutg printy a taky zaplý command test_wiring
script se bude menovat valic_wiring-minigame
za špatné připojení bude penalizace to že se to zruší celý
zde je paleta těch barev: #000000
#00021c
#1c284d
#343473
#2d5280
#4d7a99
#7497a6
#a3ccd9
#f0edd8
#732866
#a6216e
#d94c87
#d9214f
#f25565
#f27961
#993649
#b36159
#f09c60
#b38f24
#b3b324
#f7c93e
#17735f
#119955
#67b31b
#1ba683
#47cca9
#96e3c9
#2469b3
#0b8be6
#0bafe6
#f28d85
#f0bb90
hele styl té minigame je v tom obrázku co sem ti poslal
zvuky budou v .ogg a bude to umístěné někde v src složce
hele nevim jakej jazyk ale asi lua :D a chci udelat success & fail pokud budef fail tak to nic vic delat nebude pokud bude success tak vse pojede dal